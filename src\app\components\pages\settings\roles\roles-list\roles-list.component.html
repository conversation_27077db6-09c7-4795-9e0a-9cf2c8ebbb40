```html
<div class="settings-container fade-in">
  <header class="settings-header">
    <div class="header-content">
      <h1>Role Management</h1>
      <p class="subtitle">Manage user roles and their permissions</p>
    </div>
    <button class="add-button" routerLink="add">
      <span class="material-icons">add</span>
      Add Role
    </button>
  </header>

  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="!isLoading" class="roles-grid">
    <div class="role-card" *ngFor="let role of roles">
      <div class="card-header">
        <h3>{{ role.name }}</h3>
      </div>

      <div class="card-content">
        <p class="role-description">{{ role.description }}</p>
        
        <div class="permissions-section">
          <h4>Permissions</h4>
          <div class="permissions-list">
            <span class="permission-badge" *ngFor="let permId of role.permissions">
              {{ permId }}
            </span>
          </div>
        </div>
      </div>

      <div class="card-footer">
        <button class="edit-button" [routerLink]="[role.id, 'edit']">
          <span class="material-icons">edit</span>
          Edit
        </button>
        <button class="delete-button" (click)="deleteRole(role.id)">
          <span class="material-icons">delete</span>
          Delete
        </button>
      </div>
    </div>
  </div>
</div>
```