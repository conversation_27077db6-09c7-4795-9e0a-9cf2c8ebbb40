<div class="settings-container fade-in">
  <header class="settings-header">
    <div class="header-content">
      <h1>{{ roleId ? 'Edit Role' : 'Add Role' }}</h1>
      <p class="subtitle">{{ roleId ? 'Modify role details and permissions' : 'Create a new role' }}</p>
    </div>
    <button class="cancel-button" routerLink="/settings/roles">
      <span class="material-icons">close</span>
      Cancel
    </button>
  </header>

  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <form *ngIf="!isLoading" [formGroup]="roleForm" (ngSubmit)="onSubmit()" class="role-form">
    <div class="form-card">
      <div class="card-header">
        <h2>Role Details</h2>
      </div>

      <div class="card-content">
        <div class="form-group">
          <label for="name">Role Name <span class="required">*</span></label>
          <input id="name" type="text" formControlName="name" placeholder="Enter role name">
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea id="description" formControlName="description" rows="3" 
            placeholder="Enter role description"></textarea>
        </div>
      </div>
    </div>

    <div class="form-card">
      <div class="card-header">
        <h2>Permissions</h2>
      </div>

      <div class="card-content">
        <div class="permissions-grid">
          <div *ngFor="let permission of permissions" class="permission-item">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                [checked]="hasPermission(permission.id)"
                (change)="togglePermission(permission.id)"
              >
              <div class="permission-info">
                <span class="permission-name">{{ permission.name }}</span>
                <span class="permission-description">{{ permission.description }}</span>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="secondary-button" routerLink="/settings/roles">
        Cancel
      </button>
      <button 
        type="submit" 
        class="primary-button" 
        [disabled]="roleForm.invalid || isSaving">
        <span class="material-icons">{{ roleId ? 'save' : 'add' }}</span>
        {{ roleId ? (isSaving ? 'Saving...' : 'Save Changes') : (isSaving ? 'Creating...' : 'Create Role') }}
      </button>
    </div>
  </form>
</div>