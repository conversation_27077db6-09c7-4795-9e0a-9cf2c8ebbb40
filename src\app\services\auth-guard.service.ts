import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, map, switchMap } from 'rxjs';
import { AuthService } from './auth.service';
import { RoleService } from './role.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuardService implements CanActivate {
  constructor(
    private authService: AuthService,
    private roleService: RoleService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    return this.authService.getCurrentUser().pipe(
      switchMap(user => {
        if (!user) {
          this.router.navigate(['/login']);
          return new Observable<boolean>(observer => observer.next(false));
        }

        const requiredPermission = route.data['requiredPermission'];
        if (!requiredPermission) {
          return new Observable<boolean>(observer => observer.next(true));
        }

        return this.roleService.getRoleById(user.roleId).pipe(
          map(role => {
            if (!role) {
              return false;
            }

            const hasPermission = role.permissions.includes(requiredPermission);
            if (!hasPermission) {
              this.router.navigate(['/unauthorized']);
              return false;
            }

            return true;
          })
        );
      })
    );
  }
}