<div class="dashboard-container fade-in">
  <header class="dashboard-header">
    <h1>Dashboard</h1>
    <div class="actions">
      <button class="primary-button">
        <span class="material-icons">add</span>
        Add Employee
      </button>
      <button class="secondary-button">
        <span class="material-icons">download</span>
        Export
      </button>
    </div>
  </header>
  
  <div class="dashboard-stats">
    <div class="stat-card">
      <div class="stat-icon people-icon">
        <span class="material-icons">people</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-title">Total Employees</h3>
        <p class="stat-value">155</p>
        <p class="stat-trend positive">
          <span class="material-icons">trending_up</span> 
          5.2% from last month
        </p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon invoice-icon">
        <span class="material-icons">receipt</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-title">Pending Invoices</h3>
        <p class="stat-value">7</p>
        <p class="stat-trend negative">
          <span class="material-icons">trending_up</span> 
          2 more than last week
        </p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon time-icon">
        <span class="material-icons">access_time</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-title">Time Card Approval</h3>
        <p class="stat-value">23</p>
        <p class="stat-trend">
          <span class="material-icons">history</span> 
          Due in 2 days
        </p>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon attrition-icon">
        <span class="material-icons">flight_takeoff</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-title">Attrition Risk</h3>
        <p class="stat-value">3</p>
        <p class="stat-trend negative">
          <span class="material-icons">priority_high</span> 
          High risk employees
        </p>
      </div>
    </div>
  </div>
  
  <div class="dashboard-grid">
    <div class="dashboard-card">
      <div class="card-header">
        <h2>Department Distribution</h2>
      </div>
      <div class="card-content">
        <app-chart type="doughnut" [data]="departmentChartData"></app-chart>
      </div>
    </div>
    
    <div class="dashboard-card">
      <div class="card-header">
        <h2>Employment Type</h2>
      </div>
      <div class="card-content">
        <app-chart type="pie" [data]="employmentTypeChartData"></app-chart>
      </div>
    </div>
    
    <div class="dashboard-card recent-activity">
      <div class="card-header">
        <h2>Recent Activity</h2>
      </div>
      <div class="card-content">
        <ul class="activity-list">
          <li *ngFor="let activity of recentActivity" class="activity-item">
            <div class="activity-icon">
              <span class="material-icons">{{ getActivityIcon(activity.type) }}</span>
            </div>
            <div class="activity-details">
              <p class="activity-description">
                <strong>{{ activity.user }}</strong> {{ activity.details }}
              </p>
              <p class="activity-time">{{ activity.time }}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="dashboard-card">
      <div class="card-header">
        <h2>Recent Employees</h2>
        <a href="/employees" class="view-all">View All</a>
      </div>
      <div class="card-content">
        <div *ngIf="isLoading" class="loading-container">
          <app-loading-spinner></app-loading-spinner>
        </div>
        
        <table *ngIf="!isLoading" class="employees-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Department</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let employee of employees.slice(0, 5)">
              <td>
                <div class="employee-info">
                  <div class="employee-avatar" *ngIf="employee.avatarUrl">
                    <img [src]="employee.avatarUrl" [alt]="employee.firstName + ' ' + employee.lastName">
                  </div>
                  <div class="employee-avatar no-image" *ngIf="!employee.avatarUrl">
                    {{ employee.firstName[0] + employee.lastName[0] }}
                  </div>
                  <div>{{ employee.firstName }} {{ employee.lastName }}</div>
                </div>
              </td>
              <td>{{ employee.department }}</td>
              <td>
                <app-badge [type]="employee.status"></app-badge>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>