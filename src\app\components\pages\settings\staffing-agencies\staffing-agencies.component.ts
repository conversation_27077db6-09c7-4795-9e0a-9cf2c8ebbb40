import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface Agency {
  name: string;
  primaryContact: {
    name: string;
    title: string;
    email: string;
    phone: string;
  };
  invoiceSubmission: {
    frequency: string;
    lastInvoice: string;
  };
  status: 'Active' | 'Inactive';
}

@Component({
  selector: 'app-staffing-agencies',
  templateUrl: './staffing-agencies.component.html',
  styleUrls: ['./staffing-agencies.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class StaffingAgenciesComponent {
  agencies: Agency[] = [
    {
      name: 'TechTalent Solutions',
      primaryContact: {
        name: '<PERSON>',
        title: 'Account Manager',
        email: '<EMAIL>',
        phone: '(*************'
      },
      invoiceSubmission: {
        frequency: 'Weekly (Friday)',
        lastInvoice: 'Mar 15, 2025'
      },
      status: 'Active'
    },
    {
      name: 'Stellar IT Staffing',
      primaryContact: {
        name: '<PERSON>',
        title: 'Staffing Director',
        email: '<EMAIL>',
        phone: '(*************'
      },
      invoiceSubmission: {
        frequency: 'Monthly (Last day)',
        lastInvoice: 'Feb 28, 2025'
      },
      status: 'Active'
    }
  ];

  schemas = [
    {
      name: 'Standard Schema v2',
      description: 'Default mapping for most agencies',
      agencies: 'TechTalent Solutions, Stellar IT Staffing',
      created: 'Jan 15, 2025',
      expires: 'No expiration',
      status: 'Active'
    },
    {
      name: 'Legacy Format (2024)',
      description: 'Older schema for backward compatibility',
      agencies: 'Stellar IT Staffing',
      created: 'Nov 10, 2024',
      expires: 'May 15, 2025',
      status: 'Expiring Soon'
    },
    {
      name: 'TechTalent Custom Format',
      description: 'Specialized for TechTalent Solutions',
      agencies: 'TechTalent Solutions',
      created: 'Feb 05, 2025',
      expires: 'No expiration',
      status: 'Active'
    }
  ];

  statistics = {
    totalInvoices: {
      count: 18,
      period: 'Last 30 Days',
      details: '5 agencies'
    },
    averageProcessingTime: {
      value: '1.3 days',
      trend: '↓ 0.5 days from previous period'
    },
    mostActiveAgency: {
      name: 'TechTalent Solutions',
      details: '8 invoices this month'
    }
  };
}