import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmployeeService } from '../../../services/employee.service';
import { Employee } from '../../../models/employee.model';
import { LoadingSpinnerComponent } from '../../shared/loading-spinner/loading-spinner.component';
import { BadgeComponent } from '../../shared/badge/badge.component';
import { ChartComponent } from '../../shared/chart/chart.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent, BadgeComponent, ChartComponent]
})
export class DashboardComponent implements OnInit {
  employees: Employee[] = [];
  isLoading = true;
  departmentChartData = {
    labels: ['Technology', 'Product', 'Design', 'Marketing', 'Analytics', 'HR', 'Finance', 'IT'],
    datasets: [{
      data: [45, 25, 18, 22, 15, 10, 12, 8],
      backgroundColor: [
        '#C3E5AE', '#A7C5EB', '#FFD6E0', '#A7C5EB', 
        '#BEE3F8', '#D6CDEA', '#FFEE93', '#C6D8AF'
      ]
    }]
  };
  
  employmentTypeChartData = {
    labels: ['Full-time', 'Part-time', 'Contractor', 'Intern'],
    datasets: [{
      data: [85, 12, 28, 5],
      backgroundColor: ['#6366F1', '#8B5CF6', '#EC4899', '#14B8A6']
    }]
  };
  
  recentActivity = [
    { type: 'employee_added', user: 'Sarah Rodriguez', details: 'Added a new employee: James Wilson', time: '2 hours ago' },
    { type: 'invoice_uploaded', user: 'Alex Johnson', details: 'Uploaded invoice #INV2024-103', time: '4 hours ago' },
    { type: 'employee_updated', user: 'Mark Davis', details: 'Updated employee details for Charlie Brown', time: '1 day ago' },
    { type: 'report_generated', user: 'Emily Clark', details: 'Generated monthly utilization report', time: '2 days ago' }
  ];

  constructor(private employeeService: EmployeeService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.employeeService.getEmployees().subscribe(
      (employees) => {
        this.employees = employees;
        this.isLoading = false;
      },
      (error) => {
        console.error('Error loading employees:', error);
        this.isLoading = false;
      }
    );
  }
  
  getActivityIcon(type: string): string {
    switch (type) {
      case 'employee_added': return 'person_add';
      case 'invoice_uploaded': return 'receipt';
      case 'employee_updated': return 'edit';
      case 'report_generated': return 'assessment';
      default: return 'info';
    }
  }
}