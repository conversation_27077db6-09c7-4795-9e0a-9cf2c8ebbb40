import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { EmployeeService } from '../../../../services/employee.service';

interface Tab {
  id: string;
  label: string;
  icon: string;
}

@Component({
  selector: 'app-add-employee',
  templateUrl: './add-employee.component.html',
  styleUrls: ['./add-employee.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule]
})
export class AddEmployeeComponent implements OnInit {
  employeeForm: FormGroup;
  isSubmitting = false;
  currentTab = 'personal';
  selectedTeams: string[] = [];

  tabs: Tab[] = [
    { id: 'personal', label: 'Personal Info', icon: 'person' },
    { id: 'employment', label: 'Employment', icon: 'work' },
    { id: 'teams', label: 'Teams', icon: 'groups' },
    { id: 'payment', label: 'Payment', icon: 'payments' },
    { id: 'documents', label: 'Documents', icon: 'description' }
  ];

  departments = ['Technology', 'Product', 'Design', 'Marketing', 'Analytics', 'HR', 'Finance', 'IT'];
  employmentTypes = ['Full-time', 'Part-time', 'Contractor', 'Intern'];
  locationTypes = ['Office', 'Remote', 'Hybrid'];

  constructor(
    private fb: FormBuilder,
    private employeeService: EmployeeService,
    private router: Router
  ) {
    this.employeeForm = this.createForm();
  }

  ngOnInit(): void {}

  private createForm(): FormGroup {
    return this.fb.group({
      // Personal Info
      firstName: ['', [Validators.required]],
      middleName: [''],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],

      // Employment
      jobTitle: ['', [Validators.required]],
      department: ['', [Validators.required]],
      employeeType: ['Full-time', [Validators.required]],
      locationType: ['Office', [Validators.required]],
      location: ['', [Validators.required]],
      hireDate: ['', [Validators.required]],
      salary: [null],
      manager: [''],

      // Teams
      reportsTo: [''],

      // Payment
      baseSalary: [null],
      paymentType: ['salary'],
      healthInsurance: [false],
      dentalInsurance: [false],
      visionInsurance: [false],
      '401k': [false],

      // Additional
      notes: ['']
    });
  }

  setCurrentTab(tabId: string): void {
    this.currentTab = tabId;
  }

  nextTab(): void {
    const currentIndex = this.tabs.findIndex(tab => tab.id === this.currentTab);
    if (currentIndex < this.tabs.length - 1) {
      this.currentTab = this.tabs[currentIndex + 1].id;
    }
  }

  previousTab(): void {
    const currentIndex = this.tabs.findIndex(tab => tab.id === this.currentTab);
    if (currentIndex > 0) {
      this.currentTab = this.tabs[currentIndex - 1].id;
    }
  }

  showTeamDialog(): void {
    // Implement team selection dialog
    this.selectedTeams.push('Engineering Team');
  }

  removeTeam(team: string): void {
    this.selectedTeams = this.selectedTeams.filter(t => t !== team);
  }

  onSubmit(): void {
    if (this.employeeForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.employeeService.addEmployee(this.employeeForm.value).subscribe({
      next: () => {
        this.router.navigate(['/employees']);
      },
      error: (error) => {
        console.error('Error adding employee:', error);
        this.isSubmitting = false;
      }
    });
  }
}