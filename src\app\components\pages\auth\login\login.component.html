<div class="login-container">
  <div class="login-card fade-in">
    <div class="login-header">
      <h1 class="app-title">Pathfinder Analytics</h1>
      <p class="app-description">Employee management and analytics platform</p>
    </div>
    
    <div class="login-form-container">
      <h2 class="login-title">Sign In</h2>
      <p class="login-subtitle">Enter your credentials to access your account</p>
      
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <div class="form-group" [class.error]="email?.invalid && email?.touched">
          <label for="email">Email</label>
          <div class="input-container">
            <span class="material-icons input-icon">email</span>
            <input 
              type="email" 
              id="email" 
              formControlName="email" 
              placeholder="Enter your email"
              autocomplete="email"
            />
          </div>
          <div *ngIf="email?.invalid && email?.touched" class="error-message">
            <span *ngIf="email?.errors?.['required']">Email is required</span>
            <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>
        
        <div class="form-group" [class.error]="password?.invalid && password?.touched">
          <label for="password">Password</label>
          <div class="input-container">
            <span class="material-icons input-icon">lock</span>
            <input 
              type="password" 
              id="password" 
              formControlName="password" 
              placeholder="Enter your password"
              autocomplete="current-password"
            />
          </div>
          <div *ngIf="password?.invalid && password?.touched" class="error-message">
            <span *ngIf="password?.errors?.['required']">Password is required</span>
          </div>
        </div>
        
        <div class="form-options">
          <div class="remember-me">
            <input type="checkbox" id="rememberMe" formControlName="rememberMe" />
            <label for="rememberMe">Remember me</label>
          </div>
          <a href="#" class="forgot-password">Forgot password?</a>
        </div>
        
        <div *ngIf="errorMessage" class="login-error">
          <span class="material-icons error-icon">error</span>
          {{ errorMessage }}
        </div>
        
        <button 
          type="submit" 
          class="login-button" 
          [disabled]="loginForm.invalid || isLoading">
          <span *ngIf="isLoading" class="spinner"></span>
          <span *ngIf="!isLoading">Sign In</span>
        </button>
      </form>
    </div>
  </div>
</div>