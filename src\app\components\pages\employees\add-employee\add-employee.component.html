```html
<div class="page-container fade-in">
  <header class="page-header">
    <div class="header-content">
      <h1>Add New Employee</h1>
      <p class="subtitle">Enter the details for the new employee.</p>
    </div>
    <button class="cancel-button" routerLink="/employees">
      <span class="material-icons">close</span>
      Cancel
    </button>
  </header>

  <div class="employee-form-container">
    <!-- Tabs Navigation -->
    <nav class="tab-nav">
      <button 
        *ngFor="let tab of tabs" 
        class="tab-button" 
        [class.active]="currentTab === tab.id"
        (click)="setCurrentTab(tab.id)">
        <span class="material-icons">{{tab.icon}}</span>
        {{tab.label}}
      </button>
    </nav>

    <form [formGroup]="employeeForm" (ngSubmit)="onSubmit()">
      <!-- Personal Info Tab -->
      <div class="tab-content" [class.active]="currentTab === 'personal'">
        <h2 class="section-title">Basic Information</h2>
        <div class="form-grid-3">
          <div class="form-field">
            <label for="firstName">First Name <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">person</span>
              <input id="firstName" type="text" formControlName="firstName" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="middleName">Middle Name</label>
            <div class="input-container">
              <span class="material-icons">person</span>
              <input id="middleName" type="text" formControlName="middleName" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="lastName">Last Name <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">person</span>
              <input id="lastName" type="text" formControlName="lastName" />
            </div>
          </div>
        </div>

        <h2 class="section-title">Contact Information</h2>
        <div class="form-grid-2">
          <div class="form-field">
            <label for="email">Email <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">email</span>
              <input id="email" type="email" formControlName="email" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="phoneNumber">Phone Number</label>
            <div class="input-container">
              <span class="material-icons">phone</span>
              <input id="phoneNumber" type="tel" formControlName="phoneNumber" />
            </div>
          </div>
        </div>
      </div>

      <!-- Employment Tab -->
      <div class="tab-content" [class.active]="currentTab === 'employment'">
        <h2 class="section-title">Position Details</h2>
        <div class="form-grid-2">
          <div class="form-field">
            <label for="jobTitle">Job Title <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">work</span>
              <input id="jobTitle" type="text" formControlName="jobTitle" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="department">Department <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">business</span>
              <select id="department" formControlName="department">
                <option value="">Select Department</option>
                <option *ngFor="let dept of departments" [value]="dept">{{dept}}</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-grid-3">
          <div class="form-field">
            <label for="employeeType">Employment Type <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">badge</span>
              <select id="employeeType" formControlName="employeeType">
                <option *ngFor="let type of employmentTypes" [value]="type">{{type}}</option>
              </select>
            </div>
          </div>
          
          <div class="form-field">
            <label for="locationType">Location Type <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">location_on</span>
              <select id="locationType" formControlName="locationType">
                <option *ngFor="let type of locationTypes" [value]="type">{{type}}</option>
              </select>
            </div>
          </div>
          
          <div class="form-field">
            <label for="location">Location <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">place</span>
              <input id="location" type="text" formControlName="location" />
            </div>
          </div>
        </div>

        <h2 class="section-title">Employment Details</h2>
        <div class="form-grid-3">
          <div class="form-field">
            <label for="hireDate">Hire Date <span class="required">*</span></label>
            <div class="input-container">
              <span class="material-icons">event</span>
              <input id="hireDate" type="date" formControlName="hireDate" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="salary">Salary</label>
            <div class="input-container">
              <span class="material-icons">attach_money</span>
              <input id="salary" type="number" formControlName="salary" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="manager">Manager</label>
            <div class="input-container">
              <span class="material-icons">supervisor_account</span>
              <input id="manager" type="text" formControlName="manager" />
            </div>
          </div>
        </div>
      </div>

      <!-- Teams Tab -->
      <div class="tab-content" [class.active]="currentTab === 'teams'">
        <h2 class="section-title">Team Assignment</h2>
        <div class="tag-input-container">
          <div class="tag-item" *ngFor="let team of selectedTeams">
            {{team}}
            <button type="button" class="remove-tag" (click)="removeTeam(team)">
              <span class="material-icons">close</span>
            </button>
          </div>
          <button type="button" class="add-tag-button" (click)="showTeamDialog()">
            <span class="material-icons">add</span>
            Add Team
          </button>
        </div>

        <h2 class="section-title">Reporting Structure</h2>
        <div class="form-grid-2">
          <div class="form-field">
            <label for="reportsTo">Reports To</label>
            <div class="input-container">
              <span class="material-icons">supervisor_account</span>
              <input id="reportsTo" type="text" formControlName="reportsTo" />
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Tab -->
      <div class="tab-content" [class.active]="currentTab === 'payment'">
        <h2 class="section-title">Compensation</h2>
        <div class="form-grid-2">
          <div class="form-field">
            <label for="baseSalary">Base Salary</label>
            <div class="input-container">
              <span class="material-icons">attach_money</span>
              <input id="baseSalary" type="number" formControlName="baseSalary" />
            </div>
          </div>
          
          <div class="form-field">
            <label for="paymentType">Payment Type</label>
            <div class="input-container">
              <span class="material-icons">payments</span>
              <select id="paymentType" formControlName="paymentType">
                <option value="salary">Salary</option>
                <option value="hourly">Hourly</option>
              </select>
            </div>
          </div>
        </div>

        <h2 class="section-title">Benefits</h2>
        <div class="benefits-grid">
          <label class="checkbox-label">
            <input type="checkbox" formControlName="healthInsurance" />
            Health Insurance
          </label>
          <label class="checkbox-label">
            <input type="checkbox" formControlName="dentalInsurance" />
            Dental Insurance
          </label>
          <label class="checkbox-label">
            <input type="checkbox" formControlName="visionInsurance" />
            Vision Insurance
          </label>
          <label class="checkbox-label">
            <input type="checkbox" formControlName="401k" />
            401(k)
          </label>
        </div>
      </div>

      <!-- Documents Tab -->
      <div class="tab-content" [class.active]="currentTab === 'documents'">
        <div class="upload-section">
          <div class="upload-icon">
            <span class="material-icons">cloud_upload</span>
          </div>
          <h3>Upload Documents</h3>
          <p>Drag and drop files here or click to browse</p>
          <button type="button" class="upload-button">
            <span class="material-icons">add</span>
            Choose Files
          </button>
        </div>

        <div class="documents-list">
          <h2 class="section-title">Uploaded Documents</h2>
          <!-- Document list would go here -->
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button type="button" class="secondary-button" routerLink="/employees">
          Cancel
        </button>
        <div class="primary-actions">
          <button 
            type="button" 
            class="secondary-button" 
            *ngIf="currentTab !== 'personal'"
            (click)="previousTab()">
            <span class="material-icons">arrow_back</span>
            Previous
          </button>
          <button 
            type="button" 
            class="primary-button" 
            *ngIf="currentTab !== 'documents'"
            (click)="nextTab()">
            Next
            <span class="material-icons">arrow_forward</span>
          </button>
          <button 
            type="submit" 
            class="primary-button"
            *ngIf="currentTab === 'documents'"
            [disabled]="!employeeForm.valid || isSubmitting">
            <span class="material-icons">save</span>
            Save Employee
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
```