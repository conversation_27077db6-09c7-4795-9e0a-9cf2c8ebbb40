.report-container {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  color: var(--muted-foreground);
  margin: 0.5rem 0 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.primary-button,
.secondary-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.primary-button:hover {
  background-color: var(--color-primary-light);
}

.secondary-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.secondary-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

/* Filters Panel */
.filters-panel {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  padding: 1.5rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
}

.form-group select[multiple] {
  height: 120px;
}

.layout-options {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.filters-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.card-content h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
  margin: 0 0 0.5rem;
}

.card-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  margin: 0;
}

.trend-up {
  color: #10B981;
}

.trend-down {
  color: #EF4444;
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
}

.card-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.card-content {
  padding: 1.5rem;
  height: 300px;
}

/* Employee Details Table */
.employee-details-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  text-align: left;
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--color-border);
  white-space: nowrap;
}

td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--color-border);
  white-space: nowrap;
}

.utilization-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.utilization-badge.high {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.utilization-badge.medium {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.utilization-badge.low {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

.icon-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: none;
  border: none;
  border-radius: var(--radius);
  color: var(--muted-foreground);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.icon-button:hover {
  background-color: hsla(var(--secondary), 0.8);
  color: var(--color-primary);
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 4rem 0;
}

@media (max-width: 768px) {
  .report-container {
    padding: 1rem;
  }

  .report-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .primary-button,
  .secondary-button {
    flex: 1;
    justify-content: center;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }
}