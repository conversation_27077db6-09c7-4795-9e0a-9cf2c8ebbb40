{"name": "pathfinder-analytics", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "dev": "ng serve"}, "dependencies": {"@angular/animations": "^19.2.0", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/router": "^19.2.0", "@angular/material": "^19.2.0", "@angular/cdk": "^19.2.0", "chart.js": "^4.3.0", "ng2-charts": "^5.0.0", "date-fns": "^2.30.0", "rxjs": "^7.8.1", "tslib": "^2.5.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^19.2.0", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "typescript": "^5.8.2"}}