import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-not-found',
  template: `
    <div class="not-found-container">
      <div class="not-found-content">
        <h1 class="error-code">404</h1>
        <h2 class="error-title">Page Not Found</h2>
        <p class="error-description">
          The page you are looking for doesn't exist or has been moved.
        </p>
        <div class="actions">
          <button class="primary-button" (click)="goBack()">
            <span class="material-icons">arrow_back</span>
            Go Back
          </button>
          <button class="secondary-button" (click)="goHome()">
            <span class="material-icons">home</span>
            Go to Dashboard
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .not-found-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: var(--color-background);
      padding: 1rem;
    }
    
    .not-found-content {
      text-align: center;
      max-width: 500px;
      padding: 3rem 2rem;
      background-color: var(--color-card);
      border-radius: var(--radius);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .error-code {
      font-size: 5rem;
      font-weight: 700;
      color: var(--color-primary);
      margin: 0 0 1rem;
      line-height: 1;
    }
    
    .error-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 1rem;
    }
    
    .error-description {
      font-size: 1rem;
      color: var(--muted-foreground);
      margin: 0 0 2rem;
    }
    
    .actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
    }
    
    .primary-button, .secondary-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.625rem 1.25rem;
      border-radius: var(--radius);
      font-weight: 500;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .primary-button {
      background-color: var(--color-primary);
      color: white;
      border: none;
    }
    
    .primary-button:hover {
      background-color: var(--color-primary-light);
    }
    
    .secondary-button {
      background-color: transparent;
      color: var(--color-text);
      border: 1px solid var(--color-border);
    }
    
    .secondary-button:hover {
      background-color: hsla(var(--secondary), 0.8);
    }
    
    @media (max-width: 576px) {
      .actions {
        flex-direction: column;
      }
    }
  `]
})
export class NotFoundComponent {
  constructor(private router: Router) {}
  
  goBack(): void {
    window.history.back();
  }
  
  goHome(): void {
    this.router.navigate(['/dashboard']);
  }
}