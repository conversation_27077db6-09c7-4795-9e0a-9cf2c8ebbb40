.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  margin: 0.5rem 0 0;
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.edit-button,
.cancel-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.edit-button:hover {
  background-color: var(--color-primary-light);
}

.cancel-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.cancel-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

/* Form Styles */
.employee-form {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-section {
  padding: 2rem;
  border-bottom: 1px solid var(--color-border);
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-field label {
  font-size: 0.875rem;
  font-weight: 500;
}

.form-field input,
.form-field select,
.form-field textarea {
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
}

.form-field textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  padding: 1.5rem 2rem;
  background-color: var(--color-card);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* View Mode Styles */
.employee-details {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.details-section {
  padding: 2rem;
  border-bottom: 1px solid var(--color-border);
}

.details-section:last-child {
  border-bottom: none;
}

.details-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--muted-foreground);
}

.detail-item p {
  margin: 0;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .page-container {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .edit-button,
  .cancel-button {
    flex: 1;
    justify-content: center;
  }

  .form-section,
  .details-section {
    padding: 1.5rem;
  }

  .form-actions {
    flex-direction: column-reverse;
    padding: 1.5rem;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    justify-content: center;
  }
}