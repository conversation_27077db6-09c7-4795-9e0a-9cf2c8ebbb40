<div class="settings-container">
  <header class="settings-header">
    <div class="header-content">
      <h1>TimeSolv Integration</h1>
      <p class="subtitle">Configure your TimeSolv integration settings.</p>
    </div>
  </header>

  <div class="settings-card">
    <div class="card-header">
      <h2>Connection Settings</h2>
      <div class="connection-status" [class.connected]="isConnected">
        <span class="status-dot"></span>
        {{ isConnected ? 'Connected' : 'Not Connected' }}
      </div>
    </div>

    <form [formGroup]="timesolvForm" (ngSubmit)="onSubmit()" class="settings-form">
      <div class="form-group">
        <label for="apiKey">API Key <span class="required">*</span></label>
        <div class="input-container">
          <input 
            type="password" 
            id="apiKey" 
            formControlName="apiKey" 
            [placeholder]="isConnected ? '••••••••••••••••' : 'Enter your TimeSolv API key'"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="companyId">Company ID <span class="required">*</span></label>
        <div class="input-container">
          <input 
            type="text" 
            id="companyId" 
            formControlName="companyId" 
            placeholder="Enter your TimeSolv company ID"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="environment">Environment <span class="required">*</span></label>
        <div class="input-container">
          <select id="environment" formControlName="environment">
            <option value="production">Production</option>
            <option value="sandbox">Sandbox</option>
          </select>
        </div>
      </div>

      <div class="form-actions">
        <button 
          type="button" 
          class="secondary-button" 
          (click)="testConnection()" 
          [disabled]="timesolvForm.invalid || isLoading || isTesting"
        >
          <span class="material-icons">wifi_tethering</span>
          {{ isTesting ? 'Testing...' : 'Test Connection' }}
        </button>

        <button 
          *ngIf="!isConnected"
          type="submit" 
          class="primary-button" 
          [disabled]="timesolvForm.invalid || isLoading"
        >
          <span class="material-icons">link</span>
          {{ isLoading ? 'Connecting...' : 'Connect' }}
        </button>

        <button 
          *ngIf="isConnected"
          type="button" 
          class="destructive-button" 
          (click)="disconnect()"
          [disabled]="isLoading"
        >
          <span class="material-icons">link_off</span>
          {{ isLoading ? 'Disconnecting...' : 'Disconnect' }}
        </button>
      </div>
    </form>
  </div>

  <div class="settings-card" *ngIf="isConnected">
    <div class="card-header">
      <h2>Sync Settings</h2>
    </div>
    
    <div class="settings-content">
      <div class="sync-options">
        <label class="checkbox-label">
          <input type="checkbox" checked>
          <span>Auto-sync time entries daily</span>
        </label>
        
        <label class="checkbox-label">
          <input type="checkbox" checked>
          <span>Sync project information</span>
        </label>
        
        <label class="checkbox-label">
          <input type="checkbox" checked>
          <span>Sync client information</span>
        </label>
      </div>

      <div class="sync-schedule">
        <h3>Sync Schedule</h3>
        <p>Next automatic sync scheduled for: <strong>Tomorrow at 12:00 AM</strong></p>
        <button type="button" class="secondary-button">
          <span class="material-icons">sync</span>
          Sync Now
        </button>
      </div>
    </div>
  </div>
</div>