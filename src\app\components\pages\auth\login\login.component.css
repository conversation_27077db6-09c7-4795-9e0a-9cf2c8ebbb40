.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-background);
  padding: 1rem;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  padding: 2rem 2rem 1.5rem;
  text-align: center;
  border-bottom: 1px solid var(--color-border);
}

.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0 0 0.5rem;
}

.app-description {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin: 0;
}

.login-form-container {
  padding: 2rem;
}

.login-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.login-subtitle {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin: 0 0 1.5rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.error .input-container {
  border-color: var(--destructive);
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  overflow: hidden;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.2);
}

.input-icon {
  padding: 0 0.75rem;
  color: var(--muted-foreground);
}

.input-container input {
  flex: 1;
  padding: 0.75rem 0.75rem 0.75rem 0;
  border: none;
  background: none;
  font-size: 0.875rem;
  width: 100%;
}

.input-container input:focus {
  outline: none;
}

.error-message {
  color: var(--destructive);
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.forgot-password {
  color: var(--color-primary);
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: hsla(var(--destructive), 0.1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  color: var(--destructive);
}

.error-icon {
  font-size: 1rem;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: var(--radius);
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 2.75rem;
}

.login-button:hover:not(:disabled) {
  background-color: var(--color-primary-light);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}