import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { MainLayoutComponent } from './components/layout/main-layout/main-layout.component';
import { DashboardComponent } from './components/pages/dashboard/dashboard.component';
import { EmployeesListComponent } from './components/pages/employees/employees-list/employees-list.component';
import { EmployeeDetailComponent } from './components/pages/employees/employee-detail/employee-detail.component';
import { AddEmployeeComponent } from './components/pages/employees/add-employee/add-employee.component';
import { LoginComponent } from './components/pages/auth/login/login.component';
import { NotFoundComponent } from './components/pages/not-found/not-found.component';
import { TimeSolvSettingsComponent } from './components/pages/settings/timesolv/timesolv-settings.component';
import { StaffingAgenciesComponent } from './components/pages/settings/staffing-agencies/staffing-agencies.component';
import { CompanySettingsComponent } from './components/pages/settings/company/company-settings.component';
import { TimeCardUtilizationReportComponent } from './components/pages/reports/time-card-utilization/time-card-utilization-report.component';
import { RolesListComponent } from './components/pages/settings/roles/roles-list/roles-list.component';
import { RoleDetailComponent } from './components/pages/settings/roles/role-detail/role-detail.component';
import { AuthGuardService } from './services/auth-guard.service';

const routes: Routes = [
  { 
    path: '', 
    component: MainLayoutComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },
      { 
        path: 'employees', 
        component: EmployeesListComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_user_read' }
      },
      { 
        path: 'employees/add', 
        component: AddEmployeeComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_user_manage' }
      },
      { 
        path: 'employees/:id', 
        component: EmployeeDetailComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_user_read' }
      },
      { 
        path: 'employees/:id/edit', 
        component: EmployeeDetailComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_user_manage' }
      },
      { path: 'settings', redirectTo: 'settings/roles', pathMatch: 'full' },
      { 
        path: 'settings/roles', 
        component: RolesListComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_role_read' }
      },
      {
        path: 'settings/roles/add',
        component: RoleDetailComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_role_manage' }
      },
      {
        path: 'settings/roles/:id/edit',
        component: RoleDetailComponent,
        canActivate: [AuthGuardService],
        data: { requiredPermission: 'perm_role_manage' }
      },
      { path: 'settings/timesolv', component: TimeSolvSettingsComponent },
      { path: 'settings/staffing-agencies', component: StaffingAgenciesComponent },
      { path: 'settings/company', component: CompanySettingsComponent },
      { path: 'reports/time-card-utilization', component: TimeCardUtilizationReportComponent }
    ]
  },
  { path: 'login', component: LoginComponent },
  { path: '**', component: NotFoundComponent }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }