import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-badge',
  template: `
    <span class="badge" [ngClass]="getBadgeClass()">
      <span class="material-icons" *ngIf="getIcon()">{{ getIcon() }}</span>
      {{ getLabel() }}
    </span>
  `,
  styles: [`
    .badge {
      display: inline-flex;
      align-items: center;
      border-radius: 9999px;
      padding: 0.25rem 0.75rem;
      font-size: 0.75rem;
      font-weight: 500;
      gap: 0.25rem;
    }
    
    .badge-success {
      background-color: rgba(34, 197, 94, 0.1);
      color: rgb(22, 163, 74);
      border: 1px solid rgba(34, 197, 94, 0.2);
    }
    
    .badge-warning {
      background-color: rgba(245, 158, 11, 0.1);
      color: rgb(217, 119, 6);
      border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .badge-error {
      background-color: rgba(239, 68, 68, 0.1);
      color: rgb(220, 38, 38);
      border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .material-icons {
      font-size: 0.875rem;
    }
  `],
  imports: [CommonModule],
  standalone: true
})
export class BadgeComponent {
  @Input() type: string = '';
  
  getBadgeClass(): string {
    switch (this.type) {
      case 'Active': 
        return 'badge-success';
      case 'On Leave': 
        return 'badge-warning';
      case 'Terminated': 
        return 'badge-error';
      case 'Pending': 
        return 'badge-warning';
      case 'Paid': 
        return 'badge-success';
      case 'Overdue': 
        return 'badge-error';
      default: 
        return '';
    }
  }
  
  getIcon(): string {
    switch (this.type) {
      case 'Active': 
        return 'check_circle';
      case 'On Leave': 
        return 'schedule';
      case 'Terminated': 
        return 'cancel';
      case 'Pending': 
        return 'pending';
      case 'Paid': 
        return 'done';
      case 'Overdue': 
        return 'priority_high';
      default: 
        return '';
    }
  }
  
  getLabel(): string {
    return this.type;
  }
}