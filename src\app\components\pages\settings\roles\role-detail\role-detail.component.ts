import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { RoleService } from '../../../../../services/role.service';
import { Role, Permission } from '../../../../../models/auth.model';
import { LoadingSpinnerComponent } from '../../../../shared/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-role-detail',
  templateUrl: './role-detail.component.html',
  styleUrls: ['./role-detail.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent]
})
export class RoleDetailComponent implements OnInit {
  roleForm: FormGroup;
  roleId: string | null = null;
  isLoading = true;
  isSaving = false;
  permissions: Permission[] = [];
  
  constructor(
    private fb: FormBuilder,
    private roleService: RoleService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.roleForm = this.createForm();
  }

  ngOnInit(): void {
    this.roleId = this.route.snapshot.paramMap.get('id');
    this.loadPermissions();
    if (this.roleId) {
      this.loadRole();
    } else {
      this.isLoading = false;
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required]],
      description: [''],
      permissions: [[]]
    });
  }

  private loadRole(): void {
    if (!this.roleId) return;

    this.roleService.getRoleById(this.roleId).subscribe({
      next: (role) => {
        if (role) {
          this.roleForm.patchValue(role);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading role:', error);
        this.isLoading = false;
      }
    });
  }

  private loadPermissions(): void {
    this.roleService.getPermissions().subscribe({
      next: (permissions) => {
        this.permissions = permissions;
      },
      error: (error) => console.error('Error loading permissions:', error)
    });
  }

  onSubmit(): void {
    if (this.roleForm.invalid) return;

    this.isSaving = true;
    const roleData = this.roleForm.value;

    const operation = this.roleId
      ? this.roleService.updateRole(this.roleId, roleData)
      : this.roleService.addRole(roleData);

    operation.subscribe({
      next: () => {
        this.router.navigate(['/settings/roles']);
      },
      error: (error) => {
        console.error('Error saving role:', error);
        this.isSaving = false;
      }
    });
  }

  togglePermission(permissionId: string): void {
    const permissions = this.roleForm.get('permissions')?.value || [];
    const index = permissions.indexOf(permissionId);
    
    if (index === -1) {
      permissions.push(permissionId);
    } else {
      permissions.splice(index, 1);
    }
    
    this.roleForm.patchValue({ permissions });
  }

  hasPermission(permissionId: string): boolean {
    const permissions = this.roleForm.get('permissions')?.value || [];
    return permissions.includes(permissionId);
  }
}