import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-timesolv-settings',
  templateUrl: './timesolv-settings.component.html',
  styleUrls: ['./timesolv-settings.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class TimeSolvSettingsComponent {
  timesolvForm: FormGroup;
  isLoading = false;
  isTesting = false;
  isConnected = false;

  constructor(private fb: FormBuilder) {
    this.timesolvForm = this.fb.group({
      apiKey: ['', [Validators.required]],
      companyId: ['', [Validators.required]],
      environment: ['production', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.timesolvForm.invalid) return;
    
    this.isLoading = true;
    // TODO: Implement TimeSolv integration
    setTimeout(() => {
      this.isLoading = false;
      this.isConnected = true;
    }, 1000);
  }

  testConnection(): void {
    this.isTesting = true;
    // TODO: Implement connection test
    setTimeout(() => {
      this.isTesting = false;
      alert('Connection successful!');
    }, 1000);
  }

  disconnect(): void {
    if (confirm('Are you sure you want to disconnect TimeSolv integration?')) {
      this.isLoading = true;
      // TODO: Implement disconnect
      setTimeout(() => {
        this.isLoading = false;
        this.isConnected = false;
        this.timesolvForm.reset({
          environment: 'production'
        });
      }, 1000);
    }
  }
}