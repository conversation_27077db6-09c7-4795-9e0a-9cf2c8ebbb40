// Data interfaces
export interface UtilizationData {
  name: string;
  value: number;
  fill: string;
}

export interface BillableHoursData {
  month: string;
  hours: number;
}

export interface EmployeeTypeData {
  type: string;
  count: number;
  fill: string;
}

export interface LayoutOptions {
  showYTD: boolean;
  showQuarterly: boolean;
  showCurrent: boolean;
  showMonthly: boolean;
}

export interface Parameters {
  reportDate: string;
  supervisors: string[];
  teamLeads: string[];
  departments: string[];
  employmentStatus: string[];
  layoutOptions: LayoutOptions;
}

export interface TrendData {
  value: number;
  trend: number;
  trendDirection: 'up' | 'down';
}

export interface SummaryCardData {
  billableHours: TrendData;
  nonBillableHours: TrendData;
  utilization: TrendData;
  revenue: TrendData;
}

export interface EmployeeComment {
  text: string;
  date: string;
  author: string;
}

export interface EmployeeData {
  id: string;
  name: string;
  department: string;
  supervisor: string;
  teamLead: string;
  employmentStatus: string;
  firstName: string;
  lastName: string;
  employeeType: string;
  utilization: number;
  billable: number;
  nonBillable: number;
  totalHours: number;
  amount: number;
  comments: EmployeeComment[];
}

// Mock data
export const employeesData: EmployeeData[] = [
  {
    id: '1',
    name: 'John Doe',
    department: 'Engineering',
    supervisor: 'Jane Smith',
    teamLead: 'Mike Johnson',
    employmentStatus: 'Active',
    firstName: 'John',
    lastName: 'Doe',
    employeeType: 'Full-time',
    utilization: 85,
    billable: 160,
    nonBillable: 20,
    totalHours: 180,
    amount: 16000,
    comments: [
      {
        text: 'Excellent performance this month',
        date: '2025-01-15',
        author: 'Jane Smith'
      }
    ]
  }
];

export const utilizationByDepartmentData: UtilizationData[] = [
  { name: 'Engineering', value: 85, fill: 'hsla(var(--primary), 0.8)' },
  { name: 'Design', value: 75, fill: 'hsla(var(--primary), 0.6)' },
  { name: 'Marketing', value: 90, fill: 'hsla(var(--primary), 0.4)' }
];

export const billableHoursTrendData: BillableHoursData[] = [
  { month: 'Jan', hours: 160 },
  { month: 'Feb', hours: 165 },
  { month: 'Mar', hours: 170 },
  { month: 'Apr', hours: 168 },
  { month: 'May', hours: 172 },
  { month: 'Jun', hours: 175 }
];

export const employeeTypeDistributionData: EmployeeTypeData[] = [
  { type: 'Full-time', count: 150, fill: 'hsla(var(--primary), 0.8)' },
  { type: 'Part-time', count: 30, fill: 'hsla(var(--primary), 0.6)' },
  { type: 'Contract', count: 20, fill: 'hsla(var(--primary), 0.4)' }
];

export const summaryCardsData: SummaryCardData = {
  billableHours: {
    value: 2500,
    trend: 5.2,
    trendDirection: 'up'
  },
  utilization: {
    value: 85,
    trend: 2.1,
    trendDirection: 'up'
  },
  revenue: {
    value: 250000,
    trend: 4.8,
    trendDirection: 'up'
  },
  nonBillableHours: {
    value: 450,
    trend: -1.5,
    trendDirection: 'down'
  }
};

export const initialParameters: Parameters = {
  reportDate: new Date().toISOString().split('T')[0],
  supervisors: [],
  teamLeads: [],
  departments: [],
  employmentStatus: [],
  layoutOptions: {
    showYTD: true,
    showQuarterly: true,
    showCurrent: true,
    showMonthly: true
  }
};