/* Global styles for Pathfinder Analytics */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  /* Light theme colors */
  --background: 230 60% 97%; /* Light Blue-Gray */
  --foreground: 231 35% 25%; /* Dark Blue */
  --card: 0 0% 100%; /* White */
  --card-foreground: 231 35% 25%; /* Dark Blue */
  --popover: 0 0% 100%; /* White */
  --popover-foreground: 231 35% 25%; /* Dark Blue */
  --primary: 231 48% 48%; /* #3F51B5 Deep Blue */
  --primary-foreground: 0 0% 100%; /* White */
  --secondary: 230 50% 92%; /* Lighter Blue */
  --secondary-foreground: 231 40% 40%; /* Medium Blue */
  --muted: 230 40% 88%; /* Light Muted Blue */
  --muted-foreground: 230 30% 60%; /* Muted Blue Text */
  --accent: 36 100% 50%; /* #FF9800 Vibrant Orange */
  --accent-foreground: 0 0% 100%; /* White */
  --destructive: 0 72% 51%; /* Red */
  --destructive-foreground: 0 0% 98%; /* White */
  --border: 230 30% 85%; /* Light Blue Border */
  --input: 230 30% 90%; /* Light Blue Input */
  --ring: 231 48% 48%; /* Deep Blue Ring */
  --radius: 0.5rem;

  /* Color values for direct use */
  --color-primary: hsl(231, 48%, 48%);
  --color-primary-light: hsl(231, 48%, 60%);
  --color-accent: hsl(36, 100%, 50%);
  --color-background: hsl(230, 60%, 97%);
  --color-card: hsl(0, 0%, 100%);
  --color-text: hsl(231, 35%, 25%);
  --color-border: hsl(230, 30%, 85%);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--color-background);
  color: var(--color-text);
  line-height: 1.5;
}

* {
  box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  font-weight: 600;
  line-height: 1.2;
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

.card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(34, 197, 94, 0.1);
  color: rgb(22, 163, 74);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(217, 119, 6);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(220, 38, 38);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Utility classes */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.mt-4 { margin-top: 1rem; }
.mb-4 { margin-bottom: 1rem; }

/* Responsive containers */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}