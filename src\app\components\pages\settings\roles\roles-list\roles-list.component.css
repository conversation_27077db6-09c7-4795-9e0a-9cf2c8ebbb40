.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  color: var(--muted-foreground);
  margin: 0.5rem 0 0;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-button:hover {
  background-color: var(--color-primary-light);
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 4rem 0;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.role-card {
  background-color: var(--color-card);
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--color-border);
}

.card-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.card-content {
  padding: 1.25rem;
}

.role-description {
  margin: 0 0 1rem;
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.permissions-section h4 {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.75rem;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.permission-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: hsla(var(--primary), 0.1);
  color: var(--color-primary);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.card-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.edit-button,
.delete-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.edit-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.delete-button {
  background: none;
  border: 1px solid var(--destructive);
  color: var(--destructive);
}

.delete-button:hover {
  background-color: var(--destructive);
  color: white;
}

@media (max-width: 768px) {
  .settings-container {
    padding: 1rem;
  }

  .settings-header {
    flex-direction: column;
    gap: 1rem;
  }

  .add-button {
    width: 100%;
    justify-content: center;
  }

  .roles-grid {
    grid-template-columns: 1fr;
  }
}