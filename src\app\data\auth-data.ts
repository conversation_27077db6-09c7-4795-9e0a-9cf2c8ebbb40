import { Permission, Role, User } from '../models/auth.model';

export const permissions: Permission[] = [
  { id: 'perm_user_read', name: 'View Users', description: 'Can view user profiles and lists.', category: 'User Management' },
  { id: 'perm_user_manage', name: 'Manage Users', description: 'Can create, edit, and delete users.', category: 'User Management' },
  { id: 'perm_role_read', name: 'View Roles', description: 'Can view role details and lists.', category: 'Role Management' },
  { id: 'perm_role_manage', name: 'Manage Roles', description: 'Can create, edit, and delete roles.', category: 'Role Management' },
  { id: 'perm_perm_assign', name: 'Assign Permissions', description: 'Can assign permissions to roles.', category: 'Permission Management' },
  { id: 'perm_billing_view', name: 'View Billing', description: 'Can view billing information.', category: 'Billing' },
  { id: 'perm_settings_manage', name: '<PERSON><PERSON> Settings', description: 'Can manage application settings.', category: 'Application' },
];

export const roles: Role[] = [
  {
    id: 'role_admin',
    name: 'Administrator',
    description: 'Has full access to all system features.',
    permissions: permissions.map(p => p.id),
  },
  {
    id: 'role_editor',
    name: 'Editor',
    description: 'Can manage content and view users.',
    permissions: ['perm_user_read', 'perm_role_read'],
  },
  {
    id: 'role_viewer',
    name: 'Viewer',
    description: 'Can only view data, no edit capabilities.',
    permissions: ['perm_user_read'],
  },
];

export const users: User[] = [
  {
    id: 'user_1',
    name: 'Alice Wonderland',
    email: '<EMAIL>',
    roleId: 'role_admin',
    status: 'active',
    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    avatarUrl: 'https://placehold.co/100x100.png',
  },
  {
    id: 'user_2',
    name: 'Bob The Builder',
    email: '<EMAIL>',
    roleId: 'role_editor',
    status: 'active',
    lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
    avatarUrl: 'https://placehold.co/100x100.png',
  },
  {
    id: 'user_3',
    name: 'Charlie Brown',
    email: '<EMAIL>',
    roleId: 'role_viewer',
    status: 'inactive',
    avatarUrl: 'https://placehold.co/100x100.png',
  },
];

export const getRoleName = (roleId: string): string => {
  const role = roles.find(r => r.id === roleId);
  return role ? role.name : 'Unknown Role';
};

export const getPermissionName = (permissionId: string): string => {
  const permission = permissions.find(p => p.id === permissionId);
  return permission ? permission.name : 'Unknown Permission';
};