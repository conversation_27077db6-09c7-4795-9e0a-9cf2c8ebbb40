.header {
  height: 64px;
  background-color: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: hsla(var(--secondary), 0.5);
  border-radius: 9999px;
  transition: width 0.3s ease;
  width: 40px;
  height: 40px;
  overflow: hidden;
}

.search-container.expanded {
  width: 300px;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  color: var(--color-text);
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  height: 40px;
  padding: 0 0.5rem;
  font-size: 0.875rem;
  color: var(--color-text);
  outline: none;
}

.header-actions {
  display: flex;
  margin-right: 1.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  margin-left: 0.5rem;
  border-radius: 9999px;
  cursor: pointer;
  color: var(--color-text);
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: hsla(var(--secondary), 0.8);
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info {
  margin-right: 0.75rem;
  text-align: right;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.user-role {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 9999px;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .search-container.expanded {
    width: 200px;
  }
  
  .user-info {
    display: none;
  }
}