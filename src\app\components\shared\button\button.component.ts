import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-button',
  template: `
    <button 
      class="button" 
      [ngClass]="[
        variant === 'primary' ? 'button-primary' : 
        variant === 'secondary' ? 'button-secondary' : 
        variant === 'outline' ? 'button-outline' : 
        variant === 'ghost' ? 'button-ghost' : 
        variant === 'link' ? 'button-link' : 
        variant === 'destructive' ? 'button-destructive' : ''
      ]"
      [disabled]="isLoading || disabled"
      (click)="handleClick($event)">
      <span class="material-icons" *ngIf="icon && !isLoading">{{ icon }}</span>
      <span class="spinner" *ngIf="isLoading"></span>
      <span>{{ label }}</span>
    </button>
  `,
  styles: [`
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: var(--radius);
      font-weight: 500;
      font-size: 0.875rem;
      transition: all 0.2s ease;
      cursor: pointer;
    }
    
    .button-primary {
      background-color: var(--color-primary);
      color: white;
      border: none;
    }
    
    .button-primary:hover:not(:disabled) {
      background-color: var(--color-primary-light);
    }
    
    .button-secondary {
      background-color: hsla(var(--secondary), 1);
      color: var(--secondary-foreground);
      border: none;
    }
    
    .button-secondary:hover:not(:disabled) {
      background-color: hsla(var(--secondary), 0.8);
    }
    
    .button-outline {
      background-color: transparent;
      color: var(--color-text);
      border: 1px solid var(--color-border);
    }
    
    .button-outline:hover:not(:disabled) {
      background-color: hsla(var(--secondary), 0.8);
    }
    
    .button-ghost {
      background-color: transparent;
      color: var(--color-text);
      border: none;
    }
    
    .button-ghost:hover:not(:disabled) {
      background-color: hsla(var(--secondary), 0.8);
    }
    
    .button-link {
      background-color: transparent;
      color: var(--color-primary);
      border: none;
      padding: 0;
      text-decoration: underline;
    }
    
    .button-destructive {
      background-color: var(--destructive);
      color: white;
      border: none;
    }
    
    .button-destructive:hover:not(:disabled) {
      background-color: hsla(var(--destructive), 0.9);
    }
    
    .button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  `]
})
export class ButtonComponent {
  @Input() label: string = '';
  @Input() variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'destructive' = 'primary';
  @Input() icon?: string;
  @Input() isLoading: boolean = false;
  @Input() disabled: boolean = false;
  @Output() onClick = new EventEmitter<Event>();
  
  handleClick(event: Event): void {
    this.onClick.emit(event);
  }
}